import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { LanguageProvider } from './contexts/LanguageContext';
import { NotificationProvider } from './contexts/NotificationContext';
import LandingPage from './pages/LandingPage';
import AuthPage from './pages/AuthPage';
import CitizenDashboard from './pages/CitizenDashboard';
import StaffDashboard from './pages/StaffDashboard';
import IssueSubmission from './pages/IssueSubmission';
import IssueDetails from './pages/IssueDetails';
import ProtectedRoute from './components/common/ProtectedRoute';
import Layout from './components/layout/Layout';

function App() {
  return (
    <LanguageProvider>
      <AuthProvider>
        <NotificationProvider>
          <div className="min-h-screen bg-gray-50">
            <Routes>
              <Route path="/" element={<LandingPage />} />
              <Route path="/auth" element={<AuthPage />} />
              <Route path="/citizen" element={
                <ProtectedRoute userType="citizen">
                  <Layout>
                    <CitizenDashboard />
                  </Layout>
                </ProtectedRoute>
              } />
              <Route path="/staff" element={
                <ProtectedRoute userType="staff">
                  <Layout>
                    <StaffDashboard />
                  </Layout>
                </ProtectedRoute>
              } />
              <Route path="/submit-issue" element={
                <ProtectedRoute userType="citizen">
                  <Layout>
                    <IssueSubmission />
                  </Layout>
                </ProtectedRoute>
              } />
              <Route path="/issue/:id" element={
                <ProtectedRoute>
                  <Layout>
                    <IssueDetails />
                  </Layout>
                </ProtectedRoute>
              } />
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </div>
        </NotificationProvider>
      </AuthProvider>
    </LanguageProvider>
  );
}

export default App;