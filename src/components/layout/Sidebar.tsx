import React from 'react';
import { useLocation, Link } from 'react-router-dom';
import { BarChart3, Users, Settings, FileText, Wrench } from 'lucide-react';

const navigation = [
  {
    name: 'Dashboard',
    href: '/staff',
    icon: BarChart3,
  },
  {
    name: 'All Issues',
    href: '/staff/issues',
    icon: FileText,
  },
  {
    name: 'Assignments',
    href: '/staff/assignments',
    icon: Users,
  },
  {
    name: 'Departments',
    href: '/staff/departments',
    icon: Wrench,
  },
  {
    name: 'Settings',
    href: '/staff/settings',
    icon: Settings,
  },
];

export default function Sidebar() {
  const location = useLocation();

  return (
    <div className="hidden lg:flex lg:flex-shrink-0 lg:fixed lg:inset-y-0">
      <div className="flex w-64 flex-col">
        <div className="flex min-h-0 flex-1 flex-col bg-gray-800 pt-16">
          <div className="flex flex-1 flex-col overflow-y-auto pt-5 pb-4">
            <nav className="mt-5 flex-1 space-y-1 px-2">
              {navigation.map((item) => {
                const isActive = location.pathname === item.href;
                const Icon = item.icon;
                
                return (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${
                      isActive
                        ? 'bg-gray-900 text-white'
                        : 'text-gray-300 hover:bg-gray-700 hover:text-white'
                    }`}
                  >
                    <Icon
                      className={`mr-3 h-5 w-5 flex-shrink-0 ${
                        isActive ? 'text-gray-300' : 'text-gray-400 group-hover:text-gray-300'
                      }`}
                    />
                    {item.name}
                  </Link>
                );
              })}
            </nav>
          </div>
        </div>
      </div>
    </div>
  );
}