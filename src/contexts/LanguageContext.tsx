import React, { createContext, useContext, useState, ReactNode } from 'react';

interface LanguageContextType {
  language: string;
  setLanguage: (lang: string) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

const translations: Record<string, Record<string, string>> = {
  en: {
    // Landing Page
    'landing.title': 'Report & Resolve Civic Issues',
    'landing.subtitle': 'Help improve your community by reporting local issues and tracking their resolution.',
    'landing.reportIssue': 'Report an Issue',
    'landing.staffLogin': 'Staff Login',
    'landing.selectLanguage': 'Select Language',
    
    // Auth
    'auth.login': 'Sign In',
    'auth.signup': 'Sign Up',
    'auth.email': 'Email Address',
    'auth.password': 'Password',
    'auth.name': 'Full Name',
    'auth.phone': 'Phone Number',
    'auth.userType': 'I am a',
    'auth.citizen': 'Citizen',
    'auth.staff': 'Municipal Staff',
    'auth.department': 'Department',
    'auth.forgotPassword': 'Forgot Password?',
    'auth.noAccount': "Don't have an account?",
    'auth.hasAccount': 'Already have an account?',
    'auth.signUp': 'Sign Up',
    'auth.signIn': 'Sign In',
    
    // Dashboard
    'dashboard.welcome': 'Welcome',
    'dashboard.myIssues': 'My Issues',
    'dashboard.reportNew': 'Report New Issue',
    'dashboard.recent': 'Recent Reports',
    'dashboard.status.submitted': 'Submitted',
    'dashboard.status.inProgress': 'In Progress',
    'dashboard.status.resolved': 'Resolved',
    
    // Issue Submission
    'issue.submit': 'Report Issue',
    'issue.title': 'Issue Title',
    'issue.description': 'Description',
    'issue.location': 'Location',
    'issue.department': 'Department',
    'issue.priority': 'Priority',
    'issue.photos': 'Photos',
    'issue.submit.button': 'Submit Report',
    
    // Common
    'common.loading': 'Loading...',
    'common.error': 'An error occurred',
    'common.success': 'Success',
    'common.cancel': 'Cancel',
    'common.save': 'Save',
    'common.edit': 'Edit',
    'common.delete': 'Delete',
    'common.filter': 'Filter',
    'common.search': 'Search',
    'common.logout': 'Sign Out',
  },
  es: {
    'landing.title': 'Reportar y Resolver Problemas Cívicos',
    'landing.subtitle': 'Ayuda a mejorar tu comunidad reportando problemas locales y siguiendo su resolución.',
    'landing.reportIssue': 'Reportar Problema',
    'landing.staffLogin': 'Acceso Personal',
    'landing.selectLanguage': 'Seleccionar Idioma',
    // Add more Spanish translations as needed
  }
};

interface LanguageProviderProps {
  children: ReactNode;
}

export function LanguageProvider({ children }: LanguageProviderProps) {
  const [language, setLanguage] = useState('en');

  const t = (key: string): string => {
    return translations[language]?.[key] || translations.en[key] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}