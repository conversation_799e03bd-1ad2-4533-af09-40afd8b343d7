import React, { useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { useLanguage } from '../contexts/LanguageContext';
import { Mail, Lock, User, Phone, Building, Eye, EyeOff } from 'lucide-react';
import Button from '../components/common/Button';
import Input from '../components/common/Input';
import Card from '../components/common/Card';
import LoadingSpinner from '../components/common/LoadingSpinner';

export default function AuthPage() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { login, signup, isLoading } = useAuth();
  const { t } = useLanguage();
  
  const [isLogin, setIsLogin] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [userType, setUserType] = useState<'citizen' | 'staff'>(
    searchParams.get('type') as 'citizen' | 'staff' || 'citizen'
  );
  const [error, setError] = useState('');
  
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    name: '',
    phone: '',
    department: ''
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    try {
      if (isLogin) {
        await login(formData.email, formData.password, userType);
      } else {
        await signup({
          ...formData,
          type: userType,
          password: formData.password
        });
      }
      
      navigate(userType === 'citizen' ? '/citizen' : '/staff');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-green-50 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <div className="h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-lg">C</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-900">CivicReport</h1>
          </div>
          <h2 className="text-xl font-semibold text-gray-900">
            {isLogin ? t('auth.login') : t('auth.signup')}
          </h2>
        </div>

        <Card>
          {/* User Type Toggle */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-3">
              {t('auth.userType')}
            </label>
            <div className="grid grid-cols-2 gap-2">
              <button
                type="button"
                onClick={() => setUserType('citizen')}
                className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                  userType === 'citizen'
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <User className="h-5 w-5 mx-auto mb-1" />
                <span className="text-sm font-medium">{t('auth.citizen')}</span>
              </button>
              <button
                type="button"
                onClick={() => setUserType('staff')}
                className={`p-3 rounded-lg border-2 transition-all duration-200 ${
                  userType === 'staff'
                    ? 'border-green-500 bg-green-50 text-green-700'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
              >
                <Building className="h-5 w-5 mx-auto mb-1" />
                <span className="text-sm font-medium">{t('auth.staff')}</span>
              </button>
            </div>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            {!isLogin && (
              <Input
                label={t('auth.name')}
                type="text"
                name="name"
                value={formData.name}
                onChange={handleInputChange}
                leftIcon={<User />}
                required
                autoComplete="name"
              />
            )}

            <Input
              label={t('auth.email')}
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              leftIcon={<Mail />}
              required
              autoComplete="email"
            />

            {!isLogin && (
              <Input
                label={t('auth.phone')}
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                leftIcon={<Phone />}
                helperText="Optional - for SMS notifications"
                autoComplete="tel"
              />
            )}

            {!isLogin && userType === 'staff' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('auth.department')}
                </label>
                <select
                  name="department"
                  value={formData.department}
                  onChange={handleInputChange}
                  required
                  className="block w-full rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 py-2 px-3 text-base min-h-[44px]"
                >
                  <option value="">Select Department</option>
                  <option value="public-works">Public Works</option>
                  <option value="sanitation">Sanitation</option>
                  <option value="parks">Parks & Recreation</option>
                  <option value="transportation">Transportation</option>
                  <option value="utilities">Utilities</option>
                </select>
              </div>
            )}

            <Input
              label={t('auth.password')}
              type={showPassword ? 'text' : 'password'}
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              leftIcon={<Lock />}
              rightIcon={
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="p-1 hover:bg-gray-100 rounded"
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              }
              required
              autoComplete={isLogin ? 'current-password' : 'new-password'}
              helperText={!isLogin ? 'Minimum 8 characters' : undefined}
              minLength={8}
            />

            {error && (
              <div className="p-3 bg-red-50 border border-red-200 rounded-md">
                <p className="text-sm text-red-600">{error}</p>
              </div>
            )}

            <Button
              type="submit"
              className="w-full"
              size="lg"
              isLoading={isLoading}
            >
              {isLogin ? t('auth.signIn') : t('auth.signUp')}
            </Button>
          </form>

          {/* Footer */}
          <div className="mt-6 text-center space-y-2">
            {isLogin && (
              <button
                type="button"
                className="text-sm text-blue-600 hover:text-blue-700"
              >
                {t('auth.forgotPassword')}
              </button>
            )}
            
            <div className="text-sm text-gray-600">
              {isLogin ? t('auth.noAccount') : t('auth.hasAccount')}{' '}
              <button
                type="button"
                onClick={() => {
                  setIsLogin(!isLogin);
                  setError('');
                }}
                className="text-blue-600 hover:text-blue-700 font-medium"
              >
                {isLogin ? t('auth.signUp') : t('auth.signIn')}
              </button>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}