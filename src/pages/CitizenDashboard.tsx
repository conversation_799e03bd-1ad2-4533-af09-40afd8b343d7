import React from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';
import { Plus, MapPin, Clock, CheckCircle, AlertTriangle, Eye } from 'lucide-react';
import Button from '../components/common/Button';
import Card from '../components/common/Card';
import Badge from '../components/common/Badge';

// Mock data for demonstration
const mockIssues = [
  {
    id: '12345',
    title: 'Large pothole on Main Street',
    description: 'Deep pothole causing traffic issues near the intersection',
    location: '123 Main St, Downtown',
    status: 'in-progress',
    priority: 'high',
    submittedAt: '2024-01-15T10:30:00Z',
    department: 'Public Works',
    assignedTo: 'Road Maintenance Team'
  },
  {
    id: '12346',
    title: 'Broken streetlight',
    description: 'Streetlight not working for past week',
    location: '456 Oak Ave, Residential',
    status: 'submitted',
    priority: 'medium',
    submittedAt: '2024-01-14T14:20:00Z',
    department: 'Utilities'
  },
  {
    id: '12347',
    title: 'Overflowing trash bin',
    description: 'Trash bin has been overflowing for several days',
    location: '789 Pine St, Park Area',
    status: 'resolved',
    priority: 'low',
    submittedAt: '2024-01-10T09:15:00Z',
    department: 'Sanitation',
    resolvedAt: '2024-01-12T16:45:00Z'
  }
];

export default function CitizenDashboard() {
  const { t } = useLanguage();

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'submitted':
        return <Badge variant="info">{t('dashboard.status.submitted')}</Badge>;
      case 'in-progress':
        return <Badge variant="warning">{t('dashboard.status.inProgress')}</Badge>;
      case 'resolved':
        return <Badge variant="success">{t('dashboard.status.resolved')}</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'submitted':
        return <Clock className="h-5 w-5 text-blue-500" />;
      case 'in-progress':
        return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'resolved':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      default:
        return <Clock className="h-5 w-5 text-gray-500" />;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge variant="danger">High</Badge>;
      case 'medium':
        return <Badge variant="warning">Medium</Badge>;
      case 'low':
        return <Badge variant="default">Low</Badge>;
      default:
        return <Badge>{priority}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const statusCounts = {
    submitted: mockIssues.filter(issue => issue.status === 'submitted').length,
    inProgress: mockIssues.filter(issue => issue.status === 'in-progress').length,
    resolved: mockIssues.filter(issue => issue.status === 'resolved').length,
  };

  return (
    <div className="space-y-8">
      {/* Welcome Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">
            {t('dashboard.welcome')}, Jane!
          </h1>
          <p className="text-gray-600 mt-1">
            Track your civic issue reports and make a difference in your community.
          </p>
        </div>
        <Link to="/submit-issue">
          <Button size="lg" leftIcon={<Plus />}>
            {t('dashboard.reportNew')}
          </Button>
        </Link>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card hover>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Clock className="h-8 w-8 text-blue-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Submitted</p>
              <p className="text-2xl font-bold text-gray-900">{statusCounts.submitted}</p>
            </div>
          </div>
        </Card>

        <Card hover>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <AlertTriangle className="h-8 w-8 text-yellow-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">In Progress</p>
              <p className="text-2xl font-bold text-gray-900">{statusCounts.inProgress}</p>
            </div>
          </div>
        </Card>

        <Card hover>
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Resolved</p>
              <p className="text-2xl font-bold text-gray-900">{statusCounts.resolved}</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Recent Issues */}
      <Card>
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-xl font-semibold text-gray-900">
            {t('dashboard.recent')}
          </h2>
          <Link to="/citizen/issues" className="text-blue-600 hover:text-blue-700 font-medium">
            View All
          </Link>
        </div>

        <div className="space-y-4">
          {mockIssues.map((issue) => (
            <div
              key={issue.id}
              className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors duration-200"
            >
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    {getStatusIcon(issue.status)}
                    <h3 className="font-semibold text-gray-900">{issue.title}</h3>
                    {getStatusBadge(issue.status)}
                    {getPriorityBadge(issue.priority)}
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-3">{issue.description}</p>
                  
                  <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-4 space-y-2 sm:space-y-0 text-sm text-gray-500">
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 mr-1" />
                      {issue.location}
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {formatDate(issue.submittedAt)}
                    </div>
                    <div>Department: {issue.department}</div>
                  </div>

                  {issue.assignedTo && (
                    <div className="mt-2 text-sm text-blue-600">
                      Assigned to: {issue.assignedTo}
                    </div>
                  )}

                  {issue.resolvedAt && (
                    <div className="mt-2 text-sm text-green-600">
                      Resolved: {formatDate(issue.resolvedAt)}
                    </div>
                  )}
                </div>

                <div className="flex items-center space-x-2 ml-4">
                  <Link to={`/issue/${issue.id}`}>
                    <Button variant="ghost" size="sm" leftIcon={<Eye />}>
                      View
                    </Button>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {mockIssues.length === 0 && (
          <div className="text-center py-8">
            <div className="flex justify-center mb-4">
              <div className="h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center">
                <Plus className="h-8 w-8 text-gray-400" />
              </div>
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No issues reported yet</h3>
            <p className="text-gray-500 mb-4">
              Start by reporting your first civic issue to help improve your community.
            </p>
            <Link to="/submit-issue">
              <Button leftIcon={<Plus />}>
                Report Your First Issue
              </Button>
            </Link>
          </div>
        )}
      </Card>
    </div>
  );
}