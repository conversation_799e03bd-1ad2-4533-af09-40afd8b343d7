import React from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { ArrowLeft, MapPin, Calendar, User, Building, Clock, CheckCircle, MessageCircle, Camera, CreditCard as Edit } from 'lucide-react';
import Button from '../components/common/Button';
import Card from '../components/common/Card';
import Badge from '../components/common/Badge';

// Mock data for demonstration
const mockIssue = {
  id: '12345',
  title: 'Large pothole on Main Street',
  description: 'Deep pothole causing traffic issues near the intersection of Main St and Oak Ave. The hole is approximately 2 feet wide and 6 inches deep, causing vehicles to swerve dangerously to avoid it.',
  location: '123 Main St, Downtown',
  status: 'in-progress',
  priority: 'high',
  submittedAt: '2024-01-15T10:30:00Z',
  department: 'Public Works',
  submittedBy: '<PERSON>',
  submittedByEmail: '<EMAIL>',
  submittedByPhone: '+****************',
  assignedTo: '<PERSON>',
  estimatedResolution: '2024-01-20T00:00:00Z',
  photos: [
    'https://images.pexels.com/photos/2845965/pexels-photo-2845965.jpeg?auto=compress&cs=tinysrgb&w=400',
    'https://images.pexels.com/photos/1227418/pexels-photo-1227418.jpeg?auto=compress&cs=tinysrgb&w=400'
  ],
  timeline: [
    {
      id: '1',
      type: 'submitted',
      title: 'Issue Submitted',
      description: 'Issue reported by citizen',
      timestamp: '2024-01-15T10:30:00Z',
      user: 'Jane Doe'
    },
    {
      id: '2',
      type: 'assigned',
      title: 'Issue Assigned',
      description: 'Assigned to Public Works department',
      timestamp: '2024-01-15T14:20:00Z',
      user: 'System'
    },
    {
      id: '3',
      type: 'comment',
      title: 'Status Update',
      description: 'Work crew scheduled to assess the site on January 18th. Materials have been ordered.',
      timestamp: '2024-01-16T09:15:00Z',
      user: 'Mike Johnson'
    }
  ]
};

export default function IssueDetails() {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'submitted':
        return <Badge variant="info">Submitted</Badge>;
      case 'in-progress':
        return <Badge variant="warning">In Progress</Badge>;
      case 'resolved':
        return <Badge variant="success">Resolved</Badge>;
      default:
        return <Badge>{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'high':
        return <Badge variant="danger">High Priority</Badge>;
      case 'medium':
        return <Badge variant="warning">Medium Priority</Badge>;
      case 'low':
        return <Badge variant="default">Low Priority</Badge>;
      default:
        return <Badge>{priority}</Badge>;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getTimelineIcon = (type: string) => {
    switch (type) {
      case 'submitted':
        return <Clock className="h-4 w-4 text-blue-500" />;
      case 'assigned':
        return <User className="h-4 w-4 text-yellow-500" />;
      case 'comment':
        return <MessageCircle className="h-4 w-4 text-gray-500" />;
      case 'resolved':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-4">
        <Button
          variant="ghost"
          onClick={() => navigate(-1)}
          leftIcon={<ArrowLeft />}
          size="sm"
        >
          Back
        </Button>
        <div>
          <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">
            Issue #{mockIssue.id}
          </h1>
          <p className="text-gray-600 mt-1">
            Submitted {formatDate(mockIssue.submittedAt)}
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          {/* Issue Details */}
          <Card>
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="space-y-2">
                  <h2 className="text-xl font-semibold text-gray-900">{mockIssue.title}</h2>
                  <div className="flex flex-wrap gap-2">
                    {getStatusBadge(mockIssue.status)}
                    {getPriorityBadge(mockIssue.priority)}
                  </div>
                </div>
                {user?.type === 'staff' && (
                  <Button variant="outline" size="sm" leftIcon={<Edit />}>
                    Edit Issue
                  </Button>
                )}
              </div>

              <div className="prose max-w-none">
                <p className="text-gray-700 text-base leading-relaxed">
                  {mockIssue.description}
                </p>
              </div>

              {/* Issue Metadata */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">{mockIssue.location}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Building className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">{mockIssue.department}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Reported by {mockIssue.submittedBy}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">
                    ETA: {formatDate(mockIssue.estimatedResolution)}
                  </span>
                </div>
              </div>
            </div>
          </Card>

          {/* Photos */}
          {mockIssue.photos.length > 0 && (
            <Card>
              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Camera className="h-5 w-5 text-gray-500" />
                  <h3 className="text-lg font-semibold text-gray-900">Evidence Photos</h3>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {mockIssue.photos.map((photo, index) => (
                    <div key={index} className="relative group">
                      <img
                        src={photo}
                        alt={`Evidence ${index + 1}`}
                        className="w-full h-48 object-cover rounded-lg border border-gray-200"
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-opacity duration-200 rounded-lg cursor-pointer" />
                    </div>
                  ))}
                </div>
              </div>
            </Card>
          )}

          {/* Timeline */}
          <Card>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Issue Timeline</h3>
              <div className="flow-root">
                <ul className="-mb-8">
                  {mockIssue.timeline.map((event, index) => (
                    <li key={event.id}>
                      <div className="relative pb-8">
                        {index !== mockIssue.timeline.length - 1 && (
                          <span
                            className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"
                            aria-hidden="true"
                          />
                        )}
                        <div className="relative flex items-start space-x-3">
                          <div className="relative">
                            <div className="h-8 w-8 bg-white rounded-full border-2 border-gray-300 flex items-center justify-center">
                              {getTimelineIcon(event.type)}
                            </div>
                          </div>
                          <div className="min-w-0 flex-1">
                            <div>
                              <div className="text-sm">
                                <span className="font-medium text-gray-900">{event.title}</span>
                              </div>
                              <p className="mt-0.5 text-sm text-gray-500">
                                {formatDate(event.timestamp)} • {event.user}
                              </p>
                            </div>
                            <div className="mt-2 text-sm text-gray-700">
                              <p>{event.description}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </ul>
              </div>
            </div>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Status Card */}
          <Card>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900">Current Status</h3>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Status:</span>
                  {getStatusBadge(mockIssue.status)}
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Priority:</span>
                  {getPriorityBadge(mockIssue.priority)}
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Assigned to:</span>
                  <span className="text-sm font-medium text-gray-900">{mockIssue.assignedTo}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">Department:</span>
                  <span className="text-sm font-medium text-gray-900">{mockIssue.department}</span>
                </div>
              </div>
            </div>
          </Card>

          {/* Reporter Information */}
          {user?.type === 'staff' && (
            <Card>
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Reporter Information</h3>
                <div className="space-y-3 text-sm">
                  <div>
                    <span className="text-gray-500">Name:</span>
                    <p className="font-medium text-gray-900">{mockIssue.submittedBy}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Email:</span>
                    <p className="font-medium text-gray-900">{mockIssue.submittedByEmail}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">Phone:</span>
                    <p className="font-medium text-gray-900">{mockIssue.submittedByPhone}</p>
                  </div>
                </div>
              </div>
            </Card>
          )}

          {/* Actions */}
          {user?.type === 'staff' && (
            <Card>
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Actions</h3>
                <div className="space-y-2">
                  <Button className="w-full" variant="primary" size="sm">
                    Update Status
                  </Button>
                  <Button className="w-full" variant="outline" size="sm">
                    Add Comment
                  </Button>
                  <Button className="w-full" variant="outline" size="sm">
                    Assign to Staff
                  </Button>
                  <Button className="w-full" variant="outline" size="sm">
                    Send Update to Citizen
                  </Button>
                </div>
              </div>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}