import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';
import { useNotifications } from '../contexts/NotificationContext';
import { MapPin, Camera, Mic, Save, Send, ChevronLeft, ChevronRight } from 'lucide-react';
import Button from '../components/common/Button';
import Input from '../components/common/Input';
import Card from '../components/common/Card';

export default function IssueSubmission() {
  const navigate = useNavigate();
  const { t } = useLanguage();
  const { addNotification } = useNotifications();
  
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    location: '',
    department: '',
    priority: 'medium',
    photos: [] as File[],
    hasVoiceNote: false
  });

  const steps = [
    { number: 1, title: 'Issue Details', description: 'Describe the problem' },
    { number: 2, title: 'Location & Media', description: 'Where and evidence' },
    { number: 3, title: 'Review & Submit', description: 'Confirm details' }
  ];

  const departments = [
    { value: 'public-works', label: 'Public Works' },
    { value: 'sanitation', label: 'Sanitation' },
    { value: 'parks', label: 'Parks & Recreation' },
    { value: 'transportation', label: 'Transportation' },
    { value: 'utilities', label: 'Utilities' },
    { value: 'other', label: 'Other' }
  ];

  const priorities = [
    { value: 'low', label: 'Low', description: 'Not urgent, can wait' },
    { value: 'medium', label: 'Medium', description: 'Normal priority' },
    { value: 'high', label: 'High', description: 'Urgent, needs attention' }
  ];

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.name]: e.target.value
    }));
  };

  const handlePhotoUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    const validFiles = files.filter(file => file.type.startsWith('image/')).slice(0, 3);
    setFormData(prev => ({
      ...prev,
      photos: [...prev.photos, ...validFiles].slice(0, 3)
    }));
  };

  const removePhoto = (index: number) => {
    setFormData(prev => ({
      ...prev,
      photos: prev.photos.filter((_, i) => i !== index)
    }));
  };

  const getCurrentLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          // In a real app, you'd reverse geocode this
          setFormData(prev => ({
            ...prev,
            location: `${latitude.toFixed(6)}, ${longitude.toFixed(6)}`
          }));
        },
        (error) => {
          console.error('Error getting location:', error);
          addNotification({
            type: 'error',
            title: 'Location Error',
            message: 'Unable to get your current location. Please enter manually.'
          });
        }
      );
    } else {
      addNotification({
        type: 'error',
        title: 'Location Not Supported',
        message: 'Geolocation is not supported by this browser.'
      });
    }
  };

  const saveDraft = () => {
    localStorage.setItem('issue-draft', JSON.stringify(formData));
    addNotification({
      type: 'success',
      title: 'Draft Saved',
      message: 'Your issue has been saved as a draft.'
    });
  };

  const loadDraft = () => {
    const draft = localStorage.getItem('issue-draft');
    if (draft) {
      setFormData(JSON.parse(draft));
      addNotification({
        type: 'info',
        title: 'Draft Loaded',
        message: 'Your saved draft has been loaded.'
      });
    }
  };

  const validateStep = (step: number): boolean => {
    switch (step) {
      case 1:
        return formData.title.trim().length > 0 && 
               formData.description.trim().length > 0 && 
               formData.department !== '';
      case 2:
        return formData.location.trim().length > 0;
      case 3:
        return true;
      default:
        return false;
    }
  };

  const nextStep = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, 3));
    }
  };

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Clear draft
      localStorage.removeItem('issue-draft');
      
      addNotification({
        type: 'success',
        title: 'Issue Submitted',
        message: 'Your civic issue has been successfully submitted. You\'ll receive updates via email.'
      });
      
      navigate('/citizen');
    } catch (error) {
      addNotification({
        type: 'error',
        title: 'Submission Failed',
        message: 'There was an error submitting your issue. Please try again.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  React.useEffect(() => {
    // Check for draft on mount
    const draft = localStorage.getItem('issue-draft');
    if (draft) {
      const showDraft = window.confirm('You have a saved draft. Would you like to load it?');
      if (showDraft) {
        loadDraft();
      }
    }
  }, []);

  return (
    <div className="max-w-2xl mx-auto space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">
          {t('issue.submit')}
        </h1>
        <p className="text-gray-600 mt-1">
          Report a civic issue in your community and track its resolution.
        </p>
      </div>

      {/* Progress Steps */}
      <div className="flex items-center justify-between">
        {steps.map((step, index) => (
          <div key={step.number} className="flex items-center">
            <div className="flex items-center">
              <div className={`
                w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium border-2
                ${currentStep >= step.number 
                  ? 'bg-blue-600 text-white border-blue-600' 
                  : 'bg-white text-gray-500 border-gray-300'
                }
              `}>
                {step.number}
              </div>
              <div className="ml-3 hidden sm:block">
                <p className={`text-sm font-medium ${currentStep >= step.number ? 'text-blue-600' : 'text-gray-500'}`}>
                  {step.title}
                </p>
                <p className="text-xs text-gray-500">{step.description}</p>
              </div>
            </div>
            {index < steps.length - 1 && (
              <div className={`
                hidden sm:block w-16 h-0.5 mx-4
                ${currentStep > step.number ? 'bg-blue-600' : 'bg-gray-300'}
              `} />
            )}
          </div>
        ))}
      </div>

      {/* Form */}
      <Card>
        {currentStep === 1 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-900">Issue Details</h2>
            
            <Input
              label={t('issue.title')}
              value={formData.title}
              onChange={handleInputChange}
              name="title"
              placeholder="Brief, descriptive title of the issue"
              required
              helperText="Example: 'Large pothole on Main Street'"
            />

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('issue.description')}
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                required
                placeholder="Provide detailed information about the issue, including when you first noticed it and how it affects the community."
                className="block w-full rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 p-3 text-base"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('issue.department')}
              </label>
              <select
                name="department"
                value={formData.department}
                onChange={handleInputChange}
                required
                className="block w-full rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 py-2 px-3 text-base min-h-[44px]"
              >
                <option value="">Select the responsible department</option>
                {departments.map((dept) => (
                  <option key={dept.value} value={dept.value}>
                    {dept.label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                {t('issue.priority')}
              </label>
              <div className="space-y-3">
                {priorities.map((priority) => (
                  <label key={priority.value} className="flex items-center space-x-3 cursor-pointer">
                    <input
                      type="radio"
                      name="priority"
                      value={priority.value}
                      checked={formData.priority === priority.value}
                      onChange={handleInputChange}
                      className="h-4 w-4 text-blue-600 border-gray-300 focus:ring-blue-500"
                    />
                    <div>
                      <span className="font-medium text-gray-900">{priority.label}</span>
                      <p className="text-sm text-gray-500">{priority.description}</p>
                    </div>
                  </label>
                ))}
              </div>
            </div>
          </div>
        )}

        {currentStep === 2 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-900">Location & Evidence</h2>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('issue.location')}
              </label>
              <div className="flex space-x-2">
                <input
                  type="text"
                  name="location"
                  value={formData.location}
                  onChange={handleInputChange}
                  placeholder="Street address or nearby landmark"
                  required
                  className="flex-1 rounded-md border border-gray-300 shadow-sm focus:border-blue-500 focus:ring-1 focus:ring-blue-500 p-3 text-base"
                />
                <Button
                  type="button"
                  variant="outline"
                  onClick={getCurrentLocation}
                  leftIcon={<MapPin />}
                >
                  Use Current
                </Button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                {t('issue.photos')} (Optional)
              </label>
              <div className="space-y-4">
                <div className="flex items-center justify-center w-full">
                  <label className="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100">
                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                      <Camera className="w-8 h-8 mb-3 text-gray-400" />
                      <p className="mb-2 text-sm text-gray-500">
                        <span className="font-semibold">Click to upload photos</span> or drag and drop
                      </p>
                      <p className="text-xs text-gray-500">PNG, JPG up to 10MB each (max 3 photos)</p>
                    </div>
                    <input
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={handlePhotoUpload}
                      className="hidden"
                    />
                  </label>
                </div>

                {formData.photos.length > 0 && (
                  <div className="grid grid-cols-3 gap-4">
                    {formData.photos.map((photo, index) => (
                      <div key={index} className="relative">
                        <img
                          src={URL.createObjectURL(photo)}
                          alt={`Upload ${index + 1}`}
                          className="w-full h-20 object-cover rounded-md border border-gray-200"
                        />
                        <button
                          type="button"
                          onClick={() => removePhoto(index)}
                          className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full text-xs hover:bg-red-600"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Mic className="h-5 w-5 text-blue-600 mt-0.5" />
                <div>
                  <h4 className="text-sm font-medium text-blue-800">Voice Note (Coming Soon)</h4>
                  <p className="text-sm text-blue-600 mt-1">
                    Record a voice description of the issue to provide additional context.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}

        {currentStep === 3 && (
          <div className="space-y-6">
            <h2 className="text-xl font-semibold text-gray-900">Review & Submit</h2>
            
            <div className="bg-gray-50 rounded-lg p-4 space-y-4">
              <div>
                <h3 className="font-medium text-gray-900">{formData.title}</h3>
                <p className="text-gray-600 mt-1">{formData.description}</p>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-900">Department:</span>
                  <span className="ml-2 text-gray-600">
                    {departments.find(d => d.value === formData.department)?.label}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-900">Priority:</span>
                  <span className="ml-2 text-gray-600 capitalize">{formData.priority}</span>
                </div>
                <div className="sm:col-span-2">
                  <span className="font-medium text-gray-900">Location:</span>
                  <span className="ml-2 text-gray-600">{formData.location}</span>
                </div>
              </div>

              {formData.photos.length > 0 && (
                <div>
                  <span className="font-medium text-gray-900">Photos:</span>
                  <div className="grid grid-cols-3 gap-2 mt-2">
                    {formData.photos.map((photo, index) => (
                      <img
                        key={index}
                        src={URL.createObjectURL(photo)}
                        alt={`Evidence ${index + 1}`}
                        className="w-full h-16 object-cover rounded border border-gray-200"
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="text-sm font-medium text-yellow-800">What happens next?</h4>
              <ul className="text-sm text-yellow-700 mt-2 space-y-1">
                <li>• Your issue will be assigned to the appropriate department</li>
                <li>• You'll receive a confirmation email with a tracking number</li>
                <li>• Updates will be sent as work progresses</li>
                <li>• Average resolution time is 3-7 business days</li>
              </ul>
            </div>
          </div>
        )}

        {/* Navigation */}
        <div className="flex justify-between items-center mt-8 pt-6 border-t border-gray-200">
          <div className="flex space-x-2">
            <Button
              variant="ghost"
              onClick={saveDraft}
              leftIcon={<Save />}
              size="sm"
            >
              Save Draft
            </Button>
          </div>

          <div className="flex space-x-3">
            {currentStep > 1 && (
              <Button
                variant="outline"
                onClick={prevStep}
                leftIcon={<ChevronLeft />}
              >
                Previous
              </Button>
            )}
            
            {currentStep < 3 ? (
              <Button
                onClick={nextStep}
                disabled={!validateStep(currentStep)}
                rightIcon={<ChevronRight />}
              >
                Next
              </Button>
            ) : (
              <Button
                onClick={handleSubmit}
                isLoading={isSubmitting}
                leftIcon={<Send />}
                size="lg"
              >
                Submit Issue
              </Button>
            )}
          </div>
        </div>
      </Card>
    </div>
  );
}